package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.InternalBatteryInterlockReason;
import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.domain.vo.BatteryManagerDesiredSettingsVO;
import com.ebon.energy.fms.domain.vo.BatterySettingsDto;
import com.ebon.energy.fms.domain.vo.GetInverterModeSettingsDto;
import com.ebon.energy.fms.domain.vo.MinMaxValueDto;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.domain.vo.site.SiteLimits;
import lombok.Data;
import org.apache.commons.lang3.NotImplementedException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
public class RossSettingsReader extends SettingsReader {

    public RossSettingsReader(DeviceInfoAndSettings deviceSettings) {
        super(deviceSettings);
    }


    @Override
    public GetInverterModeSettingsDto getDesiredPowerModeSchedulesForPortal() {
        throw new NotImplementedException("GetInverterModeSettings has not been ported from Ross2ProductControlAdaptor's MapInverterOperationToView yet");
    }

    @Override
    public ACCoupledSettingsDto getACCoupledSettings(UniversalSettingSource source) {
        if (source == null) {
            source = UniversalSettingSource.DESIRED;
        }
        switch (source) {
            case INTENT:
                // Assuming DeviceSettings and Intent may be null, handle safely
                Boolean enable = null;
                if (getDeviceSettings() != null && getDeviceSettings().getIntent() != null) {
                    enable = getDeviceSettings().getIntent().getEnableACCoupledMode();
                }
                return new ACCoupledSettingsDto(enable);
            case REPORTED:
                return new ACCoupledSettingsDto(getDeviceSettings().getReported().isMeasuringThirdPartyInverter());
            case DESIRED:
            default:
                return new ACCoupledSettingsDto(Optional.ofNullable(getDeviceSettings()).map(x ->x.getDesired()).map(x ->x.isMeasuringThirdPartyInverter()).orElse(null));
        }
    }

    @Override
    public List<UniversalSettingId> getSupportedUniveralSettings(Integer version) {
        List<UniversalSettingId> supportedUniversalSettings = new ArrayList<>();

        supportedUniversalSettings.add(UniversalSettingId.BATTERY_SETTINGS);
        supportedUniversalSettings.add(UniversalSettingId.RELAY_SETTINGS);

        return supportedUniversalSettings;
    }

    @Override
    public BatterySettingsDto getBatterySettings(UniversalSettingSource source) {
        var minOnGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMinOnGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMinOnGridSoCLimit()).orElse(null));
        var maxOnGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMaxOnGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMaxOnGridSoCLimit()).orElse(null));
        var minOffGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMinOffGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMinOffGridSoCLimit()).orElse(null));
        var maxOffGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
        switch (source) {
            case INTENT:
                //todo
                return BatterySettingsDto.builder().build();
            case REPORTED:
                return BatterySettingsDto.builder()
                        .manufacturer(Optional.ofNullable(getDeviceSettings())
                                .map(x -> x.getReported())
                                .map(x -> x.getBatteryManager())
                                .map(x -> x.getManufacturer())
                                .map(x -> x.name())
                                .orElse(""))
                        .batteryCount(
                                MinMaxValueDto.builder()
                                        .min(getProductModelDefaults().getMinimumBatteryCount())
                                        .max(getProductModelDefaults().getMaximumBatteryCount())
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getReported())
                                                .map(x -> x.getBatteryManager())
                                                .map(x -> x.getBatteryCount())
                                                .orElse(null))
                                        .build()
                        )
                        .maxChargeCurrent(
                                MinMaxValueDto.builder()
                                        .min(getProductModelDefaults().getMinimumMaxChargeCurrentAmpere())
                                        .max(getProductModelDefaults().getMaximumMaxChargeCurrentAmpere())
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getReported())
                                                .map(x -> x.getInverterSettings())
                                                .map(x -> x.getBatterySettings())
                                                .map(x -> x.getMaxChargeCurrent())
                                                .orElse(null))
                                        .build()
                        )
                        .maxChargeVoltageV(Optional.ofNullable(deviceSettings)
                                .map(x -> x.getReported())
                                .map(x -> x.getInverterSettings())
                                .map(x -> x.getBatterySettings())
                                .map(x -> x.getMaxChargeVoltage())
                                .orElse(null))
                        .maxDischargeCurrent(
                                MinMaxValueDto.builder()
                                        .min(getProductModelDefaults().getMinimumMaxDischargeCurrentAmpere())
                                        .max(getProductModelDefaults().getMaximumMaxDischargeCurrentAmpere())
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getReported())
                                                .map(x -> x.getInverterSettings())
                                                .map(x -> x.getBatterySettings())
                                                .map(x -> x.getMaxDischargeCurrent())
                                                .orElse(null))
                                        .build()
                        )
                        .minSoc(
                                MinMaxValueDto.builder()
                                        .min(minOnGridSoC0to100)
                                        .max(maxOnGridSoC0to100)
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getReported())
                                                .map(x -> x.getInverterSettings())
                                                .map(x -> x.getBatterySettings())
                                                .map(x -> x.getMinSoCPercent())
                                                .orElse(null))
                                        .build()
                        )
                        .minOffgridSoc(
                                MinMaxValueDto.builder()
                                        .min(minOffGridSoC0to100)
                                        .max(maxOffGridSoC0to100)
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getReported())
                                                .map(x -> x.getInverterSettings())
                                                .map(x -> x.getBatterySettings())
                                                .map(x -> x.getMinOffgridSoCPercent())
                                                .orElse(null))
                                        .build()
                        )
                        .totalCapacityAh(null)
                        .batteryType(null)
                        .redbackBatterySwitch(false)
                        .build();
            default:
                return BatterySettingsDto.builder()
                        .manufacturer(Optional.ofNullable(getDeviceSettings())
                                .map(x -> x.getDesired())
                                .map(x -> x.getBatteryManager())
                                .map(x -> x.getManufacturer())
                                .map(x -> x.name())
                                .orElse(null))
                        .batteryCount(
                                MinMaxValueDto.builder()
                                        .min(getProductModelDefaults().getMinimumBatteryCount())
                                        .max(getProductModelDefaults().getMaximumBatteryCount())
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getDesired())
                                                .map(x -> x.getBatteryManager())
                                                .map(x -> x.getBatteryCount())
                                                .orElse(null))
                                        .build()
                        )
                        .maxChargeCurrent(
                                MinMaxValueDto.builder()
                                        .min(getProductModelDefaults().getMinimumMaxChargeCurrentAmpere())
                                        .max(getProductModelDefaults().getMaximumMaxChargeCurrentAmpere())
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getDesired())
                                                .map(x -> x.getInverter())
                                                .map(x -> x.getBattery())
                                                .map(x -> x.getMaxChargeCurrent())
                                                .orElse(null))
                                        .build()
                        )
                        .maxChargeVoltageV(Optional.ofNullable(deviceSettings)
                                .map(x -> x.getDesired())
                                .map(x -> x.getInverter())
                                .map(x -> x.getBattery())
                                .map(x -> x.getMaxChargeVoltage())
                                .orElse(null))
                        .maxDischargeCurrent(
                                MinMaxValueDto.builder()
                                        .min(getProductModelDefaults().getMinimumMaxDischargeCurrentAmpere())
                                        .max(getProductModelDefaults().getMaximumMaxDischargeCurrentAmpere())
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getDesired())
                                                .map(x -> x.getInverter())
                                                .map(x -> x.getBattery())
                                                .map(x -> x.getMaxDischargeCurrent())
                                                .orElse(null))
                                        .build()
                        )
                        .minSoc(
                                MinMaxValueDto.builder()
                                        .min(minOnGridSoC0to100)
                                        .max(maxOnGridSoC0to100)
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getDesired())
                                                .map(x -> x.getInverter())
                                                .map(x -> x.getBattery())
                                                .map(x -> x.getMinStateOfChargePercent())
                                                .orElse(null))
                                        .build()
                        )
                        .minOffgridSoc(
                                MinMaxValueDto.builder()
                                        .min(minOffGridSoC0to100)
                                        .max(maxOffGridSoC0to100)
                                        .value(Optional.ofNullable(getDeviceSettings())
                                                .map(x -> x.getDesired())
                                                .map(x -> x.getInverter())
                                                .map(x -> x.getBattery())
                                                .map(x -> x.getMinOffgridSoCPercent())
                                                .orElse(null))
                                        .build()
                        )
                        .totalCapacityAh(Optional.ofNullable(getDeviceSettings())
                                .map(x -> x.getDesired())
                                .map(x -> x.getBatteryManager())
                                .map(x -> x.getTotalCapacityAh())
                                .orElse(null))
                        .batteryType(null)
                        .redbackBatterySwitch(false)
                        .build();
        }
    }

    @Override
    public SiteLimits getSiteLimits() {
        return null;
    }

    @Override
    public boolean isBatteryMismatchProtectionEnabled() {
            // 提取BatteryManagerSettings，处理多层嵌套空值
        BatteryManagerDesiredSettingsVO batterySettings = Optional.ofNullable(deviceSettings)
                    .map(DeviceInfoAndSettings::getDesired)
                    .map(RossDesiredSettings::getBatteryManager)
                    .orElse(null);

            // 检查InterlocksEnabled是否为true
            if (batterySettings == null || !Boolean.TRUE.equals(batterySettings.getInterlocksEnabled())) {
                return false;
            }

            // 提取PerInterlockEnabled映射，检查是否为null
            Map<String, Boolean> protections = Optional.ofNullable(batterySettings)
                    .map(BatteryManagerDesiredSettingsVO::getPerInterlockEnabled)
                    .orElse(null);

            if (protections == null) {
                return false;
            }

            // 构造键名并检查是否存在且值为true
            String key = InternalBatteryInterlockReason.InternalBatteryCountMismatch.toString();
            return protections.containsKey(key) && Boolean.TRUE.equals(protections.get(key));
    }

}
