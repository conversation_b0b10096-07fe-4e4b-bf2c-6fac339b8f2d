package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.constants.InverterStatusConstants;
import com.ebon.energy.fms.common.enums.ConfigurationType;
import com.ebon.energy.fms.common.enums.InverterOperationMode;
import com.ebon.energy.fms.common.enums.InverterOperationModeType;
import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.po.InvertersListPO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.BatteryStatus;
import com.ebon.energy.fms.domain.vo.telemetry.Error;
import com.ebon.energy.fms.domain.vo.telemetry.InverterStatus;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.mapper.second.InvertersMapper;
import com.ebon.energy.fms.mapper.second.InvertersTagsMapper;
import com.ebon.energy.fms.mapper.third.HardwareModelMapper;
import com.ebon.energy.fms.mapper.third.ProductDailyCachesMapper;
import com.ebon.energy.fms.util.InverterStatusHelper;
import com.ebon.energy.fms.util.StreamUtil;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.common.utils.ProductUtilities.isOnline;
import static com.ebon.energy.fms.util.CalculationUtil.calculatePercentage;
import static com.ebon.energy.fms.util.SafeAccess.*;

@Service
public class InverterService {

    @Resource
    private InvertersMapper invertersMapper;

    @Resource
    private InvertersTagsMapper invertersTagsMapper;

    @Resource
    private ProductDailyCachesMapper productDailyCachesMapper;

    @Resource
    private ProductService productService;

    @Resource
    private SettingsService settingsService;
    
    @Resource
    private ConfigurationService configurationService;

    @Resource
    private InverterAttentionService inverterAttentionService;
    
    @Resource
    private HardwareModelMapper hardwareModelMapper;

    public InverterInfoVO getInverterInfo(String sn) {
        InverterInfoVO vo = productService.getWithInstallation(sn);

        SystemStatus systemStatus = vo.getSystemStatus();
        InvertersDO invertersDO = getInverter(sn);

        String inverterStatus = InverterStatusHelper.makeInverterStatus(invertersDO.getIsInWarranty(), invertersDO.getOffComms(), invertersDO.getAddress(), invertersDO.getSystemStatusTimeStampBrisbane()!=null,false,false);
        vo.setInstallationDate(inverterStatus.equals(InverterStatusConstants.PendingInstall) ? null : (invertersDO.getActiveInstallationDate() != null ? DateFormatUtils.format(invertersDO.getActiveInstallationDate(), "dd/MM/yyyy") : null));

        vo.setPlantName(invertersDO.getProductOwner());
        vo.setIsHourlyOnline(isOnline(systemStatus.getDate(), 3600000L));
        vo.setIsDailyOnline(isOnline(systemStatus.getDate(), 86400000L));
        vo.setFirmwareVersion(getOrElse(systemStatus, SystemStatus::getInverter, InverterStatus::getFirmwareVersion, invertersDO.getInverterFirmware()));
        if (StringUtils.isBlank(vo.getModelName())) {
            vo.setModelName(invertersDO.getInverterModelName());
        }

        List<HardwareModelAndFamilyDO> hardwareModels = hardwareModelMapper.selectWithFamily();
        HardwareModelAndFamilyDO hardwareModelDO = hardwareModels.stream().filter(e -> e.getName().equalsIgnoreCase(vo.getModelName())).findFirst().orElse(null);
        vo.setModelName(hardwareModelDO != null ? hardwareModelDO.getDisplayName() : vo.getModelName());

        ProductWithOwnerDO productWithOwner = productService.getProductWithOwner(sn);
        vo.setAddress(getOrElse(productWithOwner,ProductWithOwnerDO::getAddressForUI,"Not found"));
        vo.setOwnerContact(getOrElse(productWithOwner,ProductWithOwnerDO::getOwnerContact,"Not found"));
        vo.setOwnerEmail(getOrElse(productWithOwner,ProductWithOwnerDO::getOwnerEmail,"Not found"));
        vo.setOwnerFirstName(getOrElse(productWithOwner,ProductWithOwnerDO::getOwnerFirstName,"Not found"));
        vo.setOwnerLastName(getOrElse(productWithOwner,ProductWithOwnerDO::getOwnerLastName,"Not found"));
        vo.setNmi(getOrElse(productWithOwner,ProductWithOwnerDO::getNmi,"Not found"));

        //年、月光伏发电量 (kWh) 统计
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;

        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        String dateStr = today.format(formatter);

        ProductDailyCachesDO totalDO = productDailyCachesMapper.selectTotal(sn);
        if (totalDO != null) {
            vo.setAllTimePvTotal(totalDO.getTotalGeneration() == null ? null : totalDO.getTotalGeneration().setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            vo.setAllTimeTotalImport(totalDO.getTotalImport() == null ? null : totalDO.getTotalImport().setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            vo.setAllTimeTotalExport(totalDO.getTotalExport() == null ? null : totalDO.getTotalExport().setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        }

        BigDecimal dayTotal = productDailyCachesMapper.selectDayTotalGeneration(sn, dateStr);
        BigDecimal monthTotal = productDailyCachesMapper.selectMonthTotalGeneration(sn, year, month);
        BigDecimal yearTotal = productDailyCachesMapper.selectYearTotalGeneration(sn, year);
        vo.setTodayPvTotal(dayTotal == null ? null : dayTotal.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        vo.setMonthPvTotal(monthTotal == null ? null : monthTotal.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        vo.setYearPvTotal(yearTotal == null ? null : yearTotal.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());

        InverterAttentionDO attentionDO = inverterAttentionService.getBySn(sn);
        vo.setErrorStatus(attentionDO != null ? attentionDO.getStatus() : 0);

        InverterSettingsVO settingsVO = new InverterSettingsVO();

        Object settings = settingsService.getSettings(sn);
        if (settings instanceof DeviceSettingsVO) {
            DeviceSettingsVO deviceSettingsVO = (DeviceSettingsVO) settings;

            BatteryDesiredSettingsVO batteryDesiredSettingsVO = getValue(deviceSettingsVO, DeviceSettingsVO::getDesired, RossDesiredSettingsVO::getInverter, InverterDesiredSettingsVO::getBattery);
            if (batteryDesiredSettingsVO != null) {
                settingsVO.setMaxChargeCurrent(batteryDesiredSettingsVO.getMaxChargeCurrent());
                settingsVO.setMaxDischargeCurrent(batteryDesiredSettingsVO.getMaxDischargeCurrent());
                settingsVO.setMinSoCPercent(batteryDesiredSettingsVO.getMinStateOfChargePercent());
                settingsVO.setMinOffgridSoCPercent(batteryDesiredSettingsVO.getMinOffgridSoCPercent());
            }

            Map<String, Object> inverterMap = getValue(deviceSettingsVO, DeviceSettingsVO::getReported, RossReportedSettingsVO::getV2, SettingsV2ReportedVO::getInverter);
            if (inverterMap != null) {
                settingsVO.setForceChargePowerLimit(Objects.toString(inverterMap.get("ForceChargePowerLimit"), null));
                settingsVO.setBatteryAutoAwakenTimeSet(Objects.toString(inverterMap.get("BatteryAutoAwakenTimeSet"), null));
                settingsVO.setBatteryAwakenVolSet(Objects.toString(inverterMap.get("BatteryAwakenVolSet"), null));
                settingsVO.setBatteryAwakenTimeSet(Objects.toString(inverterMap.get("BatteryAwakenTimeSet"), null));
            }

            Map<String, Object> meterMap = getValue(deviceSettingsVO, DeviceSettingsVO::getReported, RossReportedSettingsVO::getV2, SettingsV2ReportedVO::getMeters);
            if (meterMap != null) {
                JSONObject grid = (JSONObject) meterMap.getOrDefault("grid", null);
                settingsVO.setMeterType(grid != null ? grid.getString("MeterTypeCode") : null);
            }
        }

        settingsVO.setBatteryModel(getValue(systemStatus, SystemStatus::getBattery, BatteryStatus::getBatteryModel
                , model -> model.equalsIgnoreCase("US2KBPL") ? "US2000B+" : model));

        ConfigurationsVO configurationsVO = configurationService.getByConfigType(sn, ConfigurationType.DeviceControl);
        DeviceControlVO deviceControlVO = JSONObject.parseObject(configurationsVO.getConfigurations(), DeviceControlVO.class);

        Optional<InverterOperationVO> inverterOperationVO = safeGet(deviceControlVO, DeviceControlVO::getInverterOperation);
        settingsVO.setAllowGridCharge(inverterOperationVO
                .map(InverterOperationVO::getType)
                .filter(t -> t.equalsIgnoreCase(InverterOperationModeType.Set.name())).orElse(null) != null);

        settingsVO.setPowerInWatts(inverterOperationVO
                .filter(e -> e.getMode().equalsIgnoreCase(InverterOperationMode.ImportPower.name()))
                .map(InverterOperationVO::getPowerInWatts)
                .orElse(null));

        List<InverterScheduleVO> inverterSchedules = inverterOperationVO
                .map(InverterOperationVO::getSchedules).orElse(null);
        InverterScheduleVO chargeSchedule = inverterSchedules.stream().filter(e -> e.getMode().equalsIgnoreCase(InverterOperationMode.ChargeBattery.name())).findFirst().orElse(null);
        if (chargeSchedule != null) {
            settingsVO.setChargeTime(chargeSchedule.getScheduleDateStr() + " " + chargeSchedule.getStartTime());
        }

        InverterScheduleVO dischargeSchedule = inverterSchedules.stream().filter(e -> e.getMode().equalsIgnoreCase(InverterOperationMode.DischargeBattery.name())).findFirst().orElse(null);
        if (dischargeSchedule != null) {
            settingsVO.setDischargeTime(dischargeSchedule.getScheduleDateStr() + " " + dischargeSchedule.getStartTime());
        }

        vo.setSettings(settingsVO);

        return vo;
    }

    public InvertersIndexVO getInverterList(InvertersListPO po) {
        InvertersIndexVO indexVO = new InvertersIndexVO();

        List<HardwareModelAndFamilyDO> hardwareModels = hardwareModelMapper.selectWithFamily();
        Map<String, HardwareModelAndFamilyDO> hardwareModelMap = hardwareModels.stream().collect(Collectors.toMap(HardwareModelAndFamilyDO::getName, Function.identity(), (first, second) -> first));

        Page<InvertersDO> page = new Page<>(po.getCurrent(), po.getPageSize());
        LambdaQueryWrapper<InvertersDO> queryWrapper = getQueryWrapper(po, hardwareModels);

        Page<InvertersDO> invertersPage = invertersMapper.selectPage(page, queryWrapper);

        if (invertersPage.getTotal() == 0) {
            indexVO.setDailyOnlinePercentage("0");
            indexVO.setHourlyOnlinePercentage("0");
            indexVO.setInverters(PageResult.toResponse(Collections.EMPTY_LIST, invertersPage.getTotal(), po.getCurrent(), po.getPageSize()));
            return indexVO;
        }

        final Map<String, InverterAttentionDO> attentionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(invertersPage.getRecords())) {
            List<InverterAttentionDO> attentions = inverterAttentionService.getByIds(invertersPage.getRecords().stream().map(InvertersDO::getSerialNumber).collect(Collectors.toList()));
            attentionMap.putAll(attentions.stream().collect(Collectors.toMap(InverterAttentionDO::getRedbackProductSn, Function.identity())));
        }

        List<InvertersVO> list = invertersPage.getRecords().stream().map(e -> {
            InvertersVO vo = new InvertersVO();
            BeanUtils.copyProperties(e, vo);

            vo.setFirmwareVersion(Strings.nullToEmpty(e.getInverterFirmware()));
            vo.setDetectedBatteryModel(Strings.nullToEmpty(e.getDetectedBatteryModel()));
            vo.setIsDailyOnline(e.getIsDailyOnline() != null ? e.getIsDailyOnline() : false);
            vo.setIsHourlyOnline(e.getIsHourlyOnline() != null ? e.getIsHourlyOnline() : false);
            vo.setIsSCCMOnline(e.getIsSCCMOnline() != null ? e.getIsSCCMOnline() : false);
            vo.setRossVersion(Strings.nullToEmpty(e.getRossVersion()));
            vo.setWindowsVersion(Strings.nullToEmpty(e.getWindowsVersion()));
            vo.setWindowsVersionSS(Strings.nullToEmpty(e.getWindowsVersionSS()));
            vo.setWatchDogVersion(Strings.nullToEmpty(e.getWatchDogVersion()));

            String modelName = e.getInverterModelName();
            HardwareModelAndFamilyDO hardwareModelDO = modelName != null ? hardwareModelMap.get(modelName) : null;
            vo.setInverterModelName(Strings.nullToEmpty(hardwareModelDO != null ? hardwareModelDO.getDisplayName() : modelName));
            if (StringUtils.isNoneBlank(e.getInverterMode())) {
                if (StringUtils.isNumeric(e.getInverterMode())) {
                    SystemStatusEnum.InverterModeValue inverterMode = SystemStatusEnum.InverterModeValue.fromValue(Integer.parseInt(e.getInverterMode()));
                } else {
                    SystemStatusEnum.InverterModeValue inverterMode = SystemStatusEnum.InverterModeValue.fromName(e.getInverterMode());
                    vo.setInverterMode(Strings.nullToEmpty(inverterMode == null ? vo.getInverterMode() : inverterMode.getDisplayName()));
                }
            } else {
                vo.setInverterMode(Strings.nullToEmpty(vo.getInverterMode()));
            }
            vo.setInstallerCompany(Strings.nullToEmpty(e.getMaintainingInstaller()));

            InverterAttentionDO attentionDO = attentionMap.get(e.getSerialNumber());
            vo.setNeedAttention(attentionDO != null ? attentionDO.getNeedAttention() : false);
            vo.setErrorStatus(attentionDO != null ? attentionDO.getStatus() : 0);
            if (attentionDO != null && StringUtils.isNotBlank(attentionDO.getLastErrors())) {
                List<Error> dbErrors = JSONObject.parseArray(attentionDO.getLastErrors(), Error.class);
                vo.setErrors(dbErrors);
            }

            return vo;
        }).collect(Collectors.toList());
        indexVO.setInverters(PageResult.toResponse(list, invertersPage.getTotal(), po.getCurrent(), po.getPageSize()));

        // 计算每日在线百分比
        LambdaQueryWrapper<InvertersDO> dailyQueryWrapper = getQueryWrapper(po, hardwareModels);
        dailyQueryWrapper.eq(InvertersDO::getIsDailyOnline, 1);
        Long dailyOnlineCnt = invertersMapper.selectCount(dailyQueryWrapper);
        String dailyOnlinePercentage = calculatePercentage(dailyOnlineCnt, invertersPage.getTotal());
        indexVO.setDailyOnlinePercentage(dailyOnlinePercentage);

        // 计算每小时在线百分比
        LambdaQueryWrapper<InvertersDO> hourlyQueryWrapper = getQueryWrapper(po, hardwareModels);
        hourlyQueryWrapper.eq(InvertersDO::getIsHourlyOnline, 1);
        Long hourlyOnlineCnt = invertersMapper.selectCount(hourlyQueryWrapper);
        String hourlyOnlinePercentage = calculatePercentage(hourlyOnlineCnt, invertersPage.getTotal());
        indexVO.setHourlyOnlinePercentage(hourlyOnlinePercentage);

        return indexVO;
    }

    private LambdaQueryWrapper getQueryWrapper(InvertersListPO po, List<HardwareModelAndFamilyDO> hardwareModels) {
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNoneBlank(po.getSerialNumber())) {
            queryWrapper.like(InvertersDO::getSerialNumber, po.getSerialNumber());
        }
        if (StringUtils.isNoneBlank(po.getRossVersion())) {
            queryWrapper.like(InvertersDO::getRossVersion, po.getRossVersion());
        }
        if (StringUtils.isNoneBlank(po.getFirmwareVersion())) {
            queryWrapper.like(InvertersDO::getInverterFirmware, po.getFirmwareVersion());
        }
        if (StringUtils.isNoneBlank(po.getInverterModelName())) {
            List<String> modelNames = StreamUtil.filterMapList(hardwareModels, e -> e.getDisplayName().contains(po.getInverterModelName()), HardwareModelAndFamilyDO::getName);
            queryWrapper.and(w -> w.in(CollectionUtils.isNotEmpty(modelNames), InvertersDO::getInverterModelName, modelNames) // IN条件（集合不为空时生效）
                    .or()
                    .like(InvertersDO::getInverterModelName, po.getInverterModelName()));
        }
        if (StringUtils.isNoneBlank(po.getInverterMode())) {
            queryWrapper.like(InvertersDO::getInverterMode, po.getInverterMode().replaceAll(" ", ""));
        }
        if (StringUtils.isNoneBlank(po.getInstallerCompany())) {
            queryWrapper.like(InvertersDO::getMaintainingInstaller, po.getInstallerCompany());
        }
        if (po.getIsDailyOnline() != null) {
            queryWrapper.eq(InvertersDO::getIsDailyOnline, po.getIsDailyOnline());
        }
        if (po.getIsHourlyOnline() != null) {
            queryWrapper.eq(InvertersDO::getIsHourlyOnline, po.getIsHourlyOnline());
        }
        if (po.getHasError() != null && po.getHasError()) {
            queryWrapper.apply("EXISTS (SELECT ia.RedbackProductSn FROM fms.dbo.InverterAttention ia where ia.RedbackProductSn=Inverters.SerialNumber and HasError=1)");
        }
        if (po.getNeedAttention() != null && po.getNeedAttention()) {
            queryWrapper.apply("EXISTS (SELECT ia.RedbackProductSn FROM fms.dbo.InverterAttention ia where ia.RedbackProductSn=Inverters.SerialNumber and NeedAttention=1)");
        }
        if (CollectionUtils.isNotEmpty(po.getTagIds())) {
            List<InverterTagsDO> invertersTags = getInvertersByTags(po.getTagIds());
            if (CollectionUtils.isNotEmpty(invertersTags)) {
                List<String> serialNumbers = invertersTags.stream().map(InverterTagsDO::getSerialNumber).collect(Collectors.toList());
                queryWrapper.in(InvertersDO::getSerialNumber, serialNumbers);
            }
        }
        if (po.getHasBattery() != null) {
            if (po.getHasBattery()) {
                queryWrapper.eq(InvertersDO::getHasBMS, 1);
            } else {
                queryWrapper.and(wrapper -> wrapper.eq(InvertersDO::getHasBMS, 0).or().isNull(InvertersDO::getHasBMS));
            }
        }

        return queryWrapper;
    }

    public Long countOnline(Boolean isDailyOnline, Boolean isHourlyOnline) {
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(isDailyOnline != null, InvertersDO::getIsDailyOnline, true);
        queryWrapper.eq(isHourlyOnline != null, InvertersDO::getIsHourlyOnline, true);
        return invertersMapper.selectCount(queryWrapper);
    }

    public List<InverterTagsDO> getInvertersByTags(List<Integer> tagIds) {
        LambdaQueryWrapper<InverterTagsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InverterTagsDO::getTagId, tagIds);
        return invertersTagsMapper.selectList(queryWrapper);
    }

    public List<InvertersDO> getInverterByQuery(String query) {
        List<InvertersDO> invertersDOS = invertersMapper.selectInvertersByQuery(query);
        return invertersDOS;
    }

    public InvertersDO getInverter(String sn) {
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvertersDO::getSerialNumber, sn);
        return invertersMapper.selectOne(queryWrapper);
    }

}
