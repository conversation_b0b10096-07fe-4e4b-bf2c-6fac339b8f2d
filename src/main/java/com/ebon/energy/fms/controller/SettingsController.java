package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.controller.request.ApllyBatterySettingRequest;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.setting.UniversalSettingValueDto;
import com.ebon.energy.fms.service.SettingsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 逆变器settings管理
 */
@Slf4j
@RestController
@RequestMapping("/api/settings")
public class SettingsController {

    @Resource
    private SettingsService settingsService;

    /**
     * 逆变器settings查询
     *
     * @param sn 序列号
     * @return
     */
    @GetMapping("/get")
    public JsonResult getSettings(String sn) {
        return JsonResult.buildSuccess(settingsService.getSettings(sn));
    }

    @GetMapping("/battery")
    public JsonResult<Map<UniversalSettingId, UniversalSettingValueDto>> getSettingsBattert(String sn) {
        return settingsService.getSettingsBattery(sn);
    }

    @PostMapping("/battery/apply")
    public void applySettingsBattert(@RequestBody ApllyBatterySettingRequest request) {
        log.info("applySettingsBattert:{}",request);
        settingsService.applySettingsBattery(request.getSn(), request);
    }


}
