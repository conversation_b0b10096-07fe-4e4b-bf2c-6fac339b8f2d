package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.ebon.energy.fms.domain.vo.product.control.SchedulesForCtrlPageDto;

import java.lang.reflect.Type;

public class SystemStatusEnum {

    public enum BatteryIndex {
        Lithium_Ion(0x00);

        private final int value;

        BatteryIndex(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @JSONCreator
        public static BatteryIndex fromValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (BatteryIndex e : values()) {
                if (e.getValue() == value) {
                    return e;
                }
            }
            throw new IllegalArgumentException("No matching battery index for value: " + value);
        }
    }

    public enum Country {
        Italy(0x00),
        Czech(0x01),
        Germany(0x02),
        Spain(0x03),
        GreeceMainland(0x04),
        Denmark(0x05),
        Belgium(0x06),
        RomaniaSpecial(0x07),
        G83Spec(0x08),
        Australia(0x09),
        AustraliaWestern(0x32),
        AUHorizon(0x44),
        AUMicroGrid(0x33, "Microgrid"),
        France(0x0a),
        China(0x0b),
        America(0x0c),
        Poland(0x0d),
        SouthAfrica(0x0e),
        Australia_L(0x0f),
        Brazil(0x10),
        ThailandM(0x11),
        ThailandP(0x12),
        Mauritius(0x13),
        Holland(0x14),
        Northern_Ireland(0x15),
        China_High_Pressure(0x16),
        France_50KhZ(0x17),
        France_60KhZ(0x18),
        Australia_Ergon(0x19, "Australia Ergon"),
        Australia_Energex(0x1a, "Australia Energex"),
        Holland_16_or_20_Amp(0x1b),
        Korea(0x1c),
        China_Station(0x1d),
        Austria(0x1e),
        India(0x1f),
        Grid_Default_50Hz(0x20),
        Warehouse(0x21),
        Phillipines(0x22),
        Ireland(0x23),
        Taiwan(0x24),
        Bulgaria(0x25),
        Barbados(0x26),
        China_Max_Pressure(0x27),
        G59_3(0x28),
        Sweden(0x29),
        Chile(0x2a),
        Brazil_Low_Voltage(0x2b),
        New_Zealand(0x2c),
        G59_2(0xfc),
        CHNspecial(0xfd),
        Default(0xfe),
        Unknown(0xff),
        Japan50Hz(0x34, "Japan 50Hz"),
        Japan60Hz(0x35, "Japan 60Hz"),
        IndiaHigher(0x36, "India Higher"),
        DewaLowVolt(0x37, "Dewa Low Volt"),
        DewaMediumVolt(0x38, "Dewa Medium Volt"),
        Slovakia(0x39),
        GreenGrid(0x3A, "Green Grid");

        private final int value;
        private final String description;

        Country(int value) {
            this(value, null);
        }

        Country(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public int getValue() {
            return value;
        }

        public String getDescription() {
            return description != null ? description : this.name();
        }

        @JSONCreator
        public static Country fromValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (Country e : values()) {
                if (e.getValue() == value) {
                    return e;
                }
            }
            throw new IllegalArgumentException("No matching country for value: " + value);
        }
    }

    public enum IoTDeviceType {
        Plug,
        AC // Air condition
    }

    public enum UpdateStatus {
        NotUpdating,
        Try1,
        Try2,
        Try3,
        Try4,
        Try5,
        UpdateFinished,
        UpdateFailed,
        UpdateFileNotExist
    }

    public enum OuijaBoardRev {
        OuijaBoardV1
    }

    @JSONType(deserializer = Generation.GenerationDeserializer.class)
    public enum Generation {
        Gen1(0),
        Gen2(1);

        private final int value;

        Generation(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @JSONCreator
        public static Generation fromValue(int value) {
            for (Generation e : values()) {
                if (e.getValue() == value) {
                    return e;
                }
            }
            throw new IllegalArgumentException("No matching generation for value: " + value);
        }

        public static class GenerationDeserializer implements ObjectDeserializer {
            @Override
            public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
                Object value = parser.parse();
                if (value == null) {
                    return null;
                }

                if (value instanceof Integer) {
                    int intValue = (Integer) value;
                    for (Generation item : Generation.values()) {
                        if (item.value == intValue) {
                            return (T) item;
                        }
                    }
                } else if (value instanceof String) {
                    String strValue = (String) value;
                    for (Generation item : Generation.values()) {
                        if (item.name().equals(strValue)) {
                            return (T) item;
                        }
                    }
                }
                throw new JSONException("无法解析枚举 Generation: " + value);
            }

            @Override
            public int getFastMatchToken() {
                return 0;
            }
        }
    }

    public enum PVStatusValue {
        Generating,
        NotGenerating,
        NotConnected
    }

    public enum GridStatusValue {
        Import,
        Export,
        Disconnected,
        Idle
    }

    public enum SystemStatusError {
        Inverter,
        Battery,
        DataNotInRange,
        None
    }

    public enum DREDCommand {
        DRM0,
        DRM1,
        DRM18,
        DRM2,
        DRM24,
        DRM3,
        DRM34,
        DRM4,
        DRM45,
        DRM5,
        DRM6,
        DRM7,
        DRM8,
        DRM86,
        DRM87,
        INACTIVE
    }

    public enum ChipsetSeries {
        Unknown,
        Series6,
        Series10
    }

    public enum Status {
        On,
        Off,
        Connected,
        Disconnected
    }

    public enum BatteryStatusValue {
        Charging,
        Discharging,
        Idle,
        Disconnected,
        Disabled
    }

    public enum BatteryDisabledReason {
        CrossCharging,
        UnderVoltage,
        Mismatch,
        Alarms,
        Other
    }

    public enum BackUpSupportStatus {
        NoBackup,
        PVMeetingBackupLoad,
        PVAndBatteryMeetingBackupLoad,
        BatteryMeetingBackupLoad,
        NoBatteries
    }

    @JSONType(deserializer = InverterModeValue.InverterModeValueDeserializer.class)
    public enum InverterModeValue {
        NoMode(0, "No Mode"),
        Auto(1, "Auto"),
        ChargeBattery(2, "Charge Battery"),
        DischargeBattery(3, "Discharge Battery"),
        ImportPower(4, "Import Power"),
        ExportPower(5, "Export Power"),
        Conserve(6, "Conserve"),
        Offgrid(7, "Off Grid"),
        Hibernate(8, "Hibernate"),
        BuyPower(9, "Buy Power"),
        SellPower(10, "Sell Power"),
        ForceChargeBattery(11, "Force Charge Battery"),
        ForceDischargeBattery(12, "Force Discharge Battery"),
        Stop(255, "Stop");

        private final int value;
        private final String displayName;

        InverterModeValue(int value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }

        public int getValue() {
            return value;
        }

        public String getDisplayName() {
            return displayName;
        }

        public static InverterModeValue fromValue(int value) {
            for (InverterModeValue e : values()) {
                if (e.getValue() == value) {
                    return e;
                }
            }
            throw new IllegalArgumentException("No matching inverter mode value for value: " + value);
        }

        public static InverterModeValue fromValue(SchedulesForCtrlPageDto.InverterOperationModeEnum value) {
            for (InverterModeValue e : values()) {
                if (e.name().equals(value.name())) {
                    return e;
                }
            }
            throw new IllegalArgumentException("No matching inverter mode value for value: " + value);
        }

        public static InverterModeValue fromName(String name) {
            for (InverterModeValue e : values()) {
                if (e.name().equalsIgnoreCase(name)) {
                    return e;
                }
            }
            throw new IllegalArgumentException("No matching inverter mode value for name: " + name);
        }

        // 添加反序列化器
        public static class InverterModeValueDeserializer implements ObjectDeserializer {
            @Override
            public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
                Object value = parser.parse();
                if (value == null) {
                    return null;
                }

                if (value instanceof Integer) {
                    int intValue = (Integer) value;
                    for (InverterModeValue item : InverterModeValue.values()) {
                        if (item.value == intValue) {
                            return (T) item;
                        }
                    }
                } else if (value instanceof String) {
                    String strValue = (String) value;
                    for (InverterModeValue item : InverterModeValue.values()) {
                        if (item.name().equals(strValue)) {
                            return (T) item;
                        }
                    }
                }
                throw new JSONException("无法解析枚举 InverterModeValue: " + value);
            }

            @Override
            public int getFastMatchToken() {
                return 0;
            }
        }
    }

    public enum ESWorkModeValue {
        NoMode(0x00),
        Wait(0x01),
        Online(0x02),
        Check(0x03),
        Battery(0x04),
        Bypass(0x08),
        Fault(0x10),
        VFStart(0x20),
        Unknown(0xFFFF);

        private final int value;

        ESWorkModeValue(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @JSONCreator
        public static ESWorkModeValue fromValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (ESWorkModeValue e : values()) {
                if (e.getValue() == value) {
                    return e;
                }
            }
            throw new IllegalArgumentException("No matching constant for value: " + value);
        }
    }

    public enum ESWorkModeValueGen1 {
        Wait(0),
        Online(1),
        Offgrid(2), // This is battery in goodwe documentation
        Bypass(3),
        Fault(4),
        VFStart(5),
        NoMode;

        private final int value;

        ESWorkModeValueGen1() {
            this.value = -1; // For NoMode which doesn't have a value
        }

        ESWorkModeValueGen1(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @JSONCreator
        public static ESWorkModeValueGen1 fromValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (ESWorkModeValueGen1 e : values()) {
                if (e.getValue() == value) {
                    return e;
                }
            }
            throw new IllegalArgumentException("No matching ESWorkModeValueGen1 for value: " + value);
        }
    }

    public enum DataVersion {
        Version1
    }

    public enum RelayID {
        Relay1,
        Relay2,
        Relay3,
        Relay4
    }

    public enum RelayWorkingMode {
        Auto,
        Schedule,
        On,
        Off
    }

    @JSONType(deserializer = OuijaBoardHardware.OuijaBoardHardwareDeserializer.class)
    public enum OuijaBoardHardware {
        Unknown,
        V4_2,
        V4_5,
        V4_6,
        V4_8,
        V4_9;

        public static class OuijaBoardHardwareDeserializer implements ObjectDeserializer {
            @Override
            public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
                Object value = parser.parse();
                if (value == null) {
                    return null;
                }

                if (value instanceof Integer) {
                    int intValue = (Integer) value;
                    for (OuijaBoardHardware item : OuijaBoardHardware.values()) {
                        if (item.ordinal() == intValue) {
                            return (T) item;
                        }
                    }
                } else if (value instanceof String) {
                    String strValue = (String) value;
                    for (OuijaBoardHardware item : OuijaBoardHardware.values()) {
                        if (item.name().equals(strValue)) {
                            return (T) item;
                        }
                    }
                }

                return (T) OuijaBoardHardware.Unknown;
            }

            @Override
            public int getFastMatchToken() {
                return 0;
            }
        }
    }

    /*public enum ExternalConnectionType {
        UNKNOWN,
        MOBILE,
        WIFI,
        ACCESS_POINT,
        ETHERNET
    }*/

    public enum SignalStrength {
        Error(-1, "Error", 5),
        VeryWeak(0, "Very Weak", 0),
        Weak(1, "Weak", 1),
        Ok(2, "Ok", 2),
        Strong(3, "Strong", 3),
        Excellent(4, "Excellent", 4);

        private final int value;
        private final String displayName;
        private final int order;

        SignalStrength(int value, String displayName, int order) {
            this.value = value;
            this.displayName = displayName;
            this.order = order;
        }

        public int getValue() {
            return value;
        }

        public String getDisplayName() {
            return displayName;
        }

        public int getOrder() {
            return order;
        }

        @JSONCreator
        public static SignalStrength fromValue(Integer value) {
            if (value == null) {
                return null;
            }

            for (SignalStrength e : values()) {
                if (e.getValue() == value) {
                    return e;
                }
            }
            throw new IllegalArgumentException("No matching signal strength for value: " + value);
        }
    }

    public enum SpecialScriptSystemVersion {
        NotExist,
        Detecting
    }

    public enum BatteryVoltageType {
        Unknown,
        HV,
        LV
    }

    public enum ConversionIssueType {
        DoNotUse, InvalidTotalPowerFactor, MissingGridData, UnsupportedInverter,
        Arm10ETotalsSwapped, MissingBatteryType, UnsupportedBatteries, UnknownBatteries,
        MissingOrUnknownPVStatus, MissingOrUnknownACLoadStatus, MissingOrUnknownBackLoadStatus,
        MissingOrUnknownWorkMode, MissingOrUnknownPowerMode, InvalidPhasePowerFactor,
        ImprovedSocCalculation, AdjustedSoCTo100Percent, MissingRTCTime,
        MissingOrUnkonwnInverterStatus, CatastrophicConversionFailure
    }


}